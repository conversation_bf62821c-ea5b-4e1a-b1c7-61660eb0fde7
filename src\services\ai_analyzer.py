"""
AI分析服务
"""
import json
import re
import time
from typing import Optional, Dict, Any, List
from openai import OpenAI
from src.config import Config
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class AIAnalyzerService:
    """AI分析服务类"""
    
    def __init__(self):
        """初始化AI分析服务"""
        self.client = OpenAI(
            api_key=Config.OPENAI_API_KEY,
            base_url=Config.OPENAI_BASE_URL,
            timeout=Config.OPENAI_TIMEOUT
        )
        self.model = Config.OPENAI_MODEL
        self.max_retries = Config.MAX_RETRIES
        
        if not Config.OPENAI_API_KEY:
            raise ValueError("OpenAI API Key未配置")
        
        logger.info("AI分析服务初始化完成")
    
    def analyze(self, prompt: str, temperature: float = 0.1) -> Optional[str]:
        """
        使用AI分析内容
        
        Args:
            prompt: 分析提示词
            temperature: 温度参数，控制输出的随机性
            
        Returns:
            AI分析结果，失败返回None
        """
        logger.info("开始AI分析")
        
        for attempt in range(self.max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {
                            "role": "system",
                            "content": "你是一个专业的网页分析专家，擅长从网页内容中提取结构化信息。请严格按照要求返回JSON格式的结果。"
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    temperature=temperature,
                    max_tokens=4000
                )
                
                result = response.choices[0].message.content
                logger.info(f"AI分析完成，结果长度: {len(result) if result else 0}")
                
                return result
                
            except Exception as e:
                logger.error(f"AI分析失败，尝试 {attempt + 1}/{self.max_retries}: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(2 * (attempt + 1))  # 递增延迟
                    continue
                else:
                    logger.error("AI分析最终失败")
                    return None
        
        return None
    
    def analyze_with_json_validation(self, prompt: str, expected_keys: list = None) -> Optional[Dict[str, Any]]:
        """
        使用AI分析内容并验证JSON格式
        
        Args:
            prompt: 分析提示词
            expected_keys: 期望的JSON键列表
            
        Returns:
            解析后的JSON字典，失败返回None
        """
        result = self.analyze(prompt)
        if not result:
            return None
        
        try:
            # 尝试解析JSON
            json_result = json.loads(result)
            
            # 验证期望的键
            if expected_keys:
                missing_keys = [key for key in expected_keys if key not in json_result]
                if missing_keys:
                    logger.warning(f"AI返回的JSON缺少期望的键: {missing_keys}")
            
            return json_result
            
        except json.JSONDecodeError as e:
            logger.error(f"AI返回结果不是有效的JSON: {e}")
            logger.error(f"AI返回内容: {result}")  # 改为ERROR级别以便看到

            # 尝试修复JSON格式
            fixed_result = self._try_fix_json(result)
            if fixed_result:
                logger.info("JSON修复成功")
                return fixed_result

            logger.error("JSON修复失败")
            return None
    
    def _try_fix_json(self, content: str) -> Optional[Dict[str, Any]]:
        """
        尝试修复损坏的JSON格式

        Args:
            content: 可能损坏的JSON内容

        Returns:
            修复后的JSON字典，失败返回None
        """
        try:
            # 清理内容
            content = content.strip()

            # 尝试直接解析
            try:
                return json.loads(content)
            except:
                pass

            # 尝试提取JSON部分
            start_idx = content.find('{')
            end_idx = content.rfind('}')

            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_part = content[start_idx:end_idx + 1]
                try:
                    return json.loads(json_part)
                except:
                    pass

            # 尝试查找```json代码块
            json_match = re.search(r'```json\s*\n(.*?)\n```', content, re.DOTALL)
            if json_match:
                json_content = json_match.group(1).strip()
                try:
                    return json.loads(json_content)
                except:
                    pass

            # 尝试查找```代码块
            code_match = re.search(r'```\s*\n(.*?)\n```', content, re.DOTALL)
            if code_match:
                code_content = code_match.group(1).strip()
                if code_content.startswith('{') and code_content.endswith('}'):
                    try:
                        return json.loads(code_content)
                    except:
                        pass

        except Exception as e:
            logger.debug(f"JSON修复失败: {e}")

        return None
    
    def analyze_search_results(self, company_name: str, search_results: list) -> Optional[Dict[str, Any]]:
        """
        分析搜索结果，找出官方网站
        
        Args:
            company_name: 公司名称
            search_results: 搜索结果列表
            
        Returns:
            分析结果字典
        """
        from src.utils.prompt_manager import PromptManager
        
        pm = PromptManager()
        prompt = pm.load_prompt(
            'search/find_official_website.txt',
            {
                'company_name': company_name,
                'search_results': json.dumps(search_results, ensure_ascii=False, indent=2)
            }
        )
        
        return self.analyze_with_json_validation(
            prompt,
            expected_keys=['official_website', 'confidence', 'reasoning']
        )
    
    def analyze_investor_relations(self, company_name: str, base_url: str, html_content: str) -> Optional[Dict[str, Any]]:
        """
        分析网页内容，提取投资者关系页面链接
        
        Args:
            company_name: 公司名称
            base_url: 基础URL
            html_content: HTML内容
            
        Returns:
            分析结果字典
        """
        from src.utils.prompt_manager import PromptManager
        
        pm = PromptManager()
        prompt = pm.load_prompt(
            'analysis/extract_investor_relations.txt',
            {
                'company_name': company_name,
                'base_url': base_url,
                'html_content': html_content[:50000]  # 限制内容长度
            }
        )
        
        return self.analyze_with_json_validation(
            prompt,
            expected_keys=['investor_relations_urls', 'confidence']
        )
    
    def analyze_news_xpath(self, company_name: str, page_url: str, html_content: str) -> Optional[Dict[str, Any]]:
        """
        分析投资者关系页面，提取新闻链接的XPath规则
        
        Args:
            company_name: 公司名称
            page_url: 页面URL
            html_content: HTML内容
            
        Returns:
            分析结果字典
        """
        from src.utils.prompt_manager import PromptManager
        
        pm = PromptManager()
        prompt = pm.load_prompt(
            'extraction/extract_news_xpath.txt',
            {
                'company_name': company_name,
                'page_url': page_url,
                'html_content': html_content[:50000]  # 限制内容长度
            }
        )
        
        return self.analyze_with_json_validation(
            prompt,
            expected_keys=['xpath_rules', 'confidence']
        )

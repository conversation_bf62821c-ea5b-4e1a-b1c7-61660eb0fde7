"""
网页抓取服务
"""
import requests
import time
from typing import Optional, List
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from src.config import Config
from src.utils.logger import setup_logger

# 禁用SSL警告
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logger = setup_logger(__name__)

class WebScraperService:
    """网页抓取服务类"""

    def __init__(self):
        """初始化网页抓取服务"""
        self.timeout = Config.SCRAPING_TIMEOUT
        self.max_retries = Config.MAX_RETRIES
        self.request_delay = Config.REQUEST_DELAY

        # 设置请求头，模拟真实浏览器
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }

        # # 创建session以支持自动解压缩
        # self.session = requests.Session()
        # self.session.headers.update(self.headers)

        # 延迟初始化DrissionPage服务（避免启动时就创建浏览器实例）
        self._drission_scraper = None

        logger.info("网页抓取服务初始化完成")

    def _get_drission_scraper(self):
        """
        获取DrissionPage抓取服务实例（延迟初始化）

        Returns:
            DrissionScraperService实例
        """
        if self._drission_scraper is None:
            try:
                from src.services.drission_scraper import DrissionScraperService
                self._drission_scraper = DrissionScraperService()
                logger.info("DrissionPage抓取服务已初始化")
            except Exception as e:
                logger.error(f"初始化DrissionPage抓取服务失败: {e}")
                raise
        return self._drission_scraper
    
    def fetch_page(self, url: str, encoding: str = 'utf-8') -> Optional[str]:
        """
        抓取网页内容
        使用三层重试策略：
        1. 第一次和第二次使用requests
        2. 第三次使用DrissionPage作为备用方案

        Args:
            url: 目标URL
            encoding: 页面编码

        Returns:
            网页HTML内容，失败返回None
        """
        logger.info(f"开始抓取页面: {url}")

        # 前两次使用requests重试
        requests_max_retries = min(2, self.max_retries)

        for attempt in range(requests_max_retries):
            try:
                logger.info(f"使用requests进行第 {attempt + 1} 次尝试: {url}")

                # 发送请求，使用session自动处理压缩
                response = requests.get(
                    url,
                    timeout=self.timeout,
                    headers=self.headers,
                    allow_redirects=True,
                    verify=False  # 暂时禁用SSL验证以处理某些网站的SSL问题
                )

                # 检查响应状态
                if response.status_code == 200:
                    # 尝试自动检测编码
                    response.encoding = 'utf-8'
                    content = response.text
                    # 清理HTML内容
                    content = self.clean_html_content(content)

                    logger.info(f"requests页面抓取成功: {url}, 内容长度: {len(content)}")
                    return content

                elif response.status_code in [301, 302, 303, 307, 308]:
                    # 处理重定向
                    redirect_url = response.headers.get('Location')
                    if redirect_url:
                        logger.info(f"页面重定向: {url} -> {redirect_url}")
                        return self.fetch_page(redirect_url, encoding)

                else:
                    logger.warning(f"requests页面抓取失败，状态码: {response.status_code}, URL: {url}")
                    if attempt < requests_max_retries - 1:
                        time.sleep(self.request_delay * (attempt + 1))
                        continue

            except requests.exceptions.Timeout:
                logger.warning(f"requests页面抓取超时，尝试 {attempt + 1}/{requests_max_retries}, URL: {url}")
                if attempt < requests_max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue

            except requests.exceptions.RequestException as e:
                logger.warning(f"requests页面抓取请求异常: {e}, URL: {url}")
                if attempt < requests_max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue

            except Exception as e:
                logger.warning(f"requests页面抓取过程中发生未知错误: {e}, URL: {url}")
                if attempt < requests_max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue

        # 如果requests重试失败，且配置允许更多重试，则使用DrissionPage
        if self.max_retries > requests_max_retries:
            logger.info(f"requests重试失败，尝试使用DrissionPage作为备用方案: {url}")
            try:
                drission_scraper = self._get_drission_scraper()
                content = drission_scraper.fetch_page(url)

                if content:
                    # 清理HTML内容
                    content = self.clean_html_content(content)
                    logger.info(f"DrissionPage页面抓取成功: {url}, 内容长度: {len(content)}")
                    return content
                else:
                    logger.warning(f"DrissionPage页面抓取失败: {url}")

            except Exception as e:
                logger.error(f"DrissionPage页面抓取异常: {e}, URL: {url}")

        logger.error(f"所有重试方案均失败: {url}")
        return None
    
    def fetch_page_with_soup(self, url: str) -> Optional[BeautifulSoup]:
        """
        抓取网页并返回BeautifulSoup对象
        
        Args:
            url: 目标URL
            
        Returns:
            BeautifulSoup对象，失败返回None
        """
        html_content = self.fetch_page(url)
        if html_content:
            try:
                soup = BeautifulSoup(html_content, 'html.parser')
                return soup
            except Exception as e:
                logger.error(f"HTML解析失败: {e}, URL: {url}")
                return None
        return None

    def fetch_page_with_js(self, url: str, wait_selector: str = None, wait_time: float = 5.0) -> Optional[str]:
        """
        专门抓取需要JavaScript渲染的页面
        直接使用DrissionPage，适用于已知需要JS渲染的页面

        Args:
            url: 目标URL
            wait_selector: 等待的CSS选择器
            wait_time: 最大等待时间（秒）

        Returns:
            网页HTML内容，失败返回None
        """
        logger.info(f"使用DrissionPage抓取JS渲染页面: {url}")

        try:
            drission_scraper = self._get_drission_scraper()
            content = drission_scraper.fetch_page_with_js_wait(url, wait_selector, wait_time)

            if content:
                # 清理HTML内容
                content = self.clean_html_content(content)
                logger.info(f"DrissionPage JS页面抓取成功: {url}, 内容长度: {len(content)}")
                return content
            else:
                logger.warning(f"DrissionPage JS页面抓取失败: {url}")
                return None

        except Exception as e:
            logger.error(f"DrissionPage JS页面抓取异常: {e}, URL: {url}")
            return None

    def extract_links(self, html_content: str, base_url: str) -> list:
        """
        从HTML内容中提取所有链接
        
        Args:
            html_content: HTML内容
            base_url: 基础URL，用于处理相对链接
            
        Returns:
            链接列表
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            links = []
            
            for a_tag in soup.find_all('a', href=True):
                href = a_tag['href']
                text = a_tag.get_text(strip=True)
                
                # 处理相对链接
                if href.startswith('http'):
                    absolute_url = href
                else:
                    absolute_url = urljoin(base_url, href)
                
                links.append({
                    'url': absolute_url,
                    'text': text,
                    'href': href
                })
            
            logger.info(f"从页面提取到 {len(links)} 个链接")
            return links
            
        except Exception as e:
            logger.error(f"链接提取失败: {e}")
            return []
    
    def clean_html_content(self, html_content: str) -> str:
        """
        深度清理HTML内容，移除无关标签和脚本，大幅减少内容长度

        Args:
            html_content: 原始HTML内容

        Returns:
            清理后的HTML内容
        """
        try:
            original_length = len(html_content)
            soup = BeautifulSoup(html_content, 'html.parser')

            # 第一步：移除整个head标签及其内容
            head_tag = soup.find('head')
            if head_tag:
                head_tag.decompose()

            # 第二步：移除无关的HTML标签
            unwanted_tags = [
                'script',      # JavaScript代码
                'style',       # CSS样式
                'link',        # 外部资源引用
                'meta',        # 元数据
                'noscript',    # 无脚本内容
                'iframe',      # 内嵌框架
                'embed',       # 嵌入内容
                'object',      # 对象
                'svg',         # SVG图形
                'canvas',      # 画布
                'audio',       # 音频
                'video',       # 视频
                'source',      # 媒体源
                'track'        # 媒体轨道
            ]

            for tag_name in unwanted_tags:
                for tag in soup.find_all(tag_name):
                    tag.decompose()

            # 第三步：移除内联事件处理器属性
            event_attributes = [
                'onclick', 'onload', 'onunload', 'onchange', 'onsubmit',
                'onreset', 'onselect', 'onblur', 'onfocus', 'onkeydown',
                'onkeypress', 'onkeyup', 'onmouseover', 'onmouseout',
                'onmousedown', 'onmouseup', 'onmousemove', 'ondblclick',
                'ondrag', 'ondrop', 'onscroll', 'onresize', 'onerror'
            ]

            for tag in soup.find_all():
                for attr in event_attributes:
                    if tag.has_attr(attr):
                        del tag[attr]

            # 第四步：移除不必要的属性，保留重要属性
            important_attributes = {
                'a': ['href', 'title'],
                'img': ['src', 'alt'],
                'form': ['action', 'method'],
                'input': ['type', 'name', 'value'],
                'button': ['type', 'name'],
                'table': ['class'],
                'div': ['class', 'id'],
                'span': ['class'],
                'p': ['class'],
                'h1': ['class'], 'h2': ['class'], 'h3': ['class'],
                'h4': ['class'], 'h5': ['class'], 'h6': ['class'],
                'ul': ['class'], 'ol': ['class'], 'li': ['class'],
                'nav': ['class'], 'section': ['class'], 'article': ['class']
            }

            for tag in soup.find_all():
                if tag.name in important_attributes:
                    # 保留重要属性
                    attrs_to_keep = important_attributes[tag.name]
                    current_attrs = list(tag.attrs.keys())
                    for attr in current_attrs:
                        if attr not in attrs_to_keep:
                            del tag[attr]
                else:
                    # 对于其他标签，移除所有属性（除了少数通用属性）
                    universal_attrs = ['class', 'id']
                    current_attrs = list(tag.attrs.keys())
                    for attr in current_attrs:
                        if attr not in universal_attrs:
                            del tag[attr]

            # 第五步：移除HTML注释
            from bs4 import Comment
            for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
                comment.extract()

            # 第六步：移除空白标签和多余的空白字符
            for tag in soup.find_all():
                if not tag.get_text(strip=True) and not tag.find_all():
                    tag.decompose()

            # 第七步：压缩HTML结构
            cleaned_html = str(soup)

            # 移除多余的空白字符和换行
            import re
            cleaned_html = re.sub(r'\s+', ' ', cleaned_html)  # 多个空白字符替换为单个空格
            cleaned_html = re.sub(r'>\s+<', '><', cleaned_html)  # 移除标签间的空白

            cleaned_length = len(cleaned_html)
            reduction_percentage = ((original_length - cleaned_length) / original_length) * 100

            logger.info(f"HTML内容深度清理完成:")
            logger.info(f"  原始长度: {original_length:,} 字符")
            logger.info(f"  清理后长度: {cleaned_length:,} 字符")
            logger.info(f"  减少比例: {reduction_percentage:.1f}%")

            return cleaned_html

        except Exception as e:
            logger.error(f"HTML深度清理失败: {e}")
            return html_content

    def split_html_content(self, html_content: str, max_length: int = 50000) -> List[str]:
        """
        智能分割HTML内容，确保每个分片保持结构完整性

        Args:
            html_content: 要分割的HTML内容
            max_length: 每个分片的最大长度

        Returns:
            HTML内容分片列表
        """
        try:
            if len(html_content) <= max_length:
                return [html_content]

            logger.info(f"HTML内容过长({len(html_content):,}字符)，开始智能分割")

            soup = BeautifulSoup(html_content, 'html.parser')
            fragments = []

            # 优先按这些块级元素分割
            block_selectors = [
                'section',
                'article',
                'main',
                'div.content',
                'div.main',
                'div[class*="content"]',
                'div[class*="main"]',
                'div[id*="content"]',
                'div[id*="main"]',
                'div'
            ]

            # 尝试按块级元素分割
            for selector in block_selectors:
                blocks = soup.select(selector)
                if blocks and len(blocks) > 1:
                    logger.info(f"使用选择器 '{selector}' 找到 {len(blocks)} 个块级元素")

                    current_fragment = ""
                    fragment_count = 0

                    for block in blocks:
                        block_html = str(block)

                        # 如果当前片段加上新块会超过限制，保存当前片段
                        if current_fragment and len(current_fragment) + len(block_html) > max_length:
                            if current_fragment.strip():
                                fragments.append(current_fragment)
                                fragment_count += 1
                                logger.debug(f"创建分片 {fragment_count}: {len(current_fragment):,} 字符")
                            current_fragment = block_html
                        else:
                            current_fragment += block_html

                    # 添加最后一个片段
                    if current_fragment.strip():
                        fragments.append(current_fragment)
                        fragment_count += 1
                        logger.debug(f"创建分片 {fragment_count}: {len(current_fragment):,} 字符")

                    if fragments:
                        break

            # 如果块级分割失败，按字符长度强制分割
            if not fragments:
                logger.warning("块级分割失败，使用字符长度强制分割")
                content = html_content
                fragment_count = 0

                while content:
                    if len(content) <= max_length:
                        fragments.append(content)
                        fragment_count += 1
                        break

                    # 寻找合适的分割点（尽量在标签边界）
                    split_point = max_length
                    for i in range(max_length - 100, max_length):
                        if i < len(content) and content[i] == '>':
                            split_point = i + 1
                            break

                    fragment = content[:split_point]
                    fragments.append(fragment)
                    fragment_count += 1
                    logger.debug(f"强制分片 {fragment_count}: {len(fragment):,} 字符")

                    content = content[split_point:]

            logger.info(f"HTML内容分割完成，共生成 {len(fragments)} 个分片")
            for i, fragment in enumerate(fragments, 1):
                logger.debug(f"分片 {i}: {len(fragment):,} 字符")

            return fragments

        except Exception as e:
            logger.error(f"HTML内容分割失败: {e}")
            return [html_content]

    def is_valid_url(self, url: str) -> bool:
        """
        检查URL是否有效
        
        Args:
            url: 要检查的URL
            
        Returns:
            URL是否有效
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False

    def close(self):
        """关闭所有资源"""
        if self._drission_scraper:
            try:
                self._drission_scraper.close()
                self._drission_scraper = None
                logger.info("DrissionPage资源已清理")
            except Exception as e:
                logger.warning(f"清理DrissionPage资源时出现警告: {e}")

    def __del__(self):
        """析构函数，确保资源被清理"""
        self.close()

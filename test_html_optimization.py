#!/usr/bin/env python3
"""
HTML内容清理和分批处理优化测试脚本
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.search_research import SearchResearchClass
from src.services.web_scraper import WebScraperService
from src.services.ai_analyzer import AIAnalyzerService
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

def test_html_cleaning():
    """测试HTML内容清理功能"""
    logger.info("=" * 60)
    logger.info("测试HTML内容清理功能")
    logger.info("=" * 60)
    
    web_scraper = WebScraperService()
    
    # 创建一个包含各种HTML元素的测试内容
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Page</title>
        <meta charset="utf-8">
        <link rel="stylesheet" href="style.css">
        <script src="script.js"></script>
        <style>
            body { margin: 0; }
            .test { color: red; }
        </style>
    </head>
    <body>
        <header id="header" class="main-header">
            <nav class="navigation">
                <ul>
                    <li><a href="/home">Home</a></li>
                    <li><a href="/about">About</a></li>
                    <li><a href="/investors" onclick="track()">Investors</a></li>
                </ul>
            </nav>
        </header>
        
        <main class="content">
            <section class="news-section">
                <h1>Latest News</h1>
                <div class="news-grid">
                    <article class="news-item">
                        <h2><a href="/news/1">News Title 1</a></h2>
                        <p>News content here...</p>
                    </article>
                    <article class="news-item">
                        <h2><a href="/news/2">News Title 2</a></h2>
                        <p>News content here...</p>
                    </article>
                </div>
            </section>
            
            <aside class="sidebar">
                <div class="widget">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="/press-releases">Press Releases</a></li>
                        <li><a href="/sec-filings">SEC Filings</a></li>
                    </ul>
                </div>
            </aside>
        </main>
        
        <footer class="footer">
            <p>&copy; 2024 Test Company</p>
        </footer>
        
        <script>
            function track() {
                console.log('Tracking...');
            }
        </script>
        
        <!-- This is a comment -->
        <div style="display:none;" data-tracking="true" aria-label="hidden">Hidden content</div>
    </body>
    </html>
    """
    
    logger.info(f"原始HTML长度: {len(test_html):,} 字符")
    
    # 测试HTML清理
    cleaned_html = web_scraper.clean_html_content(test_html)
    
    logger.info(f"清理后HTML长度: {len(cleaned_html):,} 字符")
    logger.info(f"减少比例: {((len(test_html) - len(cleaned_html)) / len(test_html) * 100):.1f}%")
    
    # 显示清理后的内容（前500字符）
    logger.info("清理后的HTML内容预览:")
    logger.info("-" * 40)
    logger.info(cleaned_html[:500] + "..." if len(cleaned_html) > 500 else cleaned_html)
    logger.info("-" * 40)
    
    return cleaned_html

def test_html_splitting():
    """测试HTML内容分割功能"""
    logger.info("=" * 60)
    logger.info("测试HTML内容分割功能")
    logger.info("=" * 60)
    
    web_scraper = WebScraperService()
    
    # 创建一个较长的HTML内容用于测试分割
    large_html = """
    <html>
    <body>
        <main class="content">
    """
    
    # 添加多个section来模拟长内容
    for i in range(20):
        section_html = f"""
            <section class="section-{i}">
                <h2>Section {i}</h2>
                <div class="content-block">
                    <p>This is a long paragraph with lots of content to make the HTML large enough for testing the splitting functionality. 
                    It contains various information about the company, news, press releases, and investor relations.</p>
                    <ul class="news-list">
                        <li><a href="/news/{i}-1">News Item {i}-1</a></li>
                        <li><a href="/news/{i}-2">News Item {i}-2</a></li>
                        <li><a href="/news/{i}-3">News Item {i}-3</a></li>
                    </ul>
                    <div class="widget">
                        <h3>Related Links</h3>
                        <a href="/press-releases/{i}">Press Release {i}</a>
                        <a href="/sec-filings/{i}">SEC Filing {i}</a>
                    </div>
                </div>
            </section>
        """
        large_html += section_html
    
    large_html += """
        </main>
    </body>
    </html>
    """
    
    logger.info(f"大HTML内容长度: {len(large_html):,} 字符")
    
    # 测试分割功能
    fragments = web_scraper.split_html_content(large_html, max_length=5000)
    
    logger.info(f"分割结果: {len(fragments)} 个分片")
    for i, fragment in enumerate(fragments, 1):
        logger.info(f"分片 {i}: {len(fragment):,} 字符")
    
    return fragments

def test_batch_analysis():
    """测试分批分析功能"""
    logger.info("=" * 60)
    logger.info("测试分批分析功能")
    logger.info("=" * 60)
    
    ai_analyzer = AIAnalyzerService()
    
    # 模拟HTML分片
    html_fragments = [
        """<div class="news-section">
            <h2>Press Releases</h2>
            <ul class="news-list">
                <li><a href="/press/release-1">Release 1</a></li>
                <li><a href="/press/release-2">Release 2</a></li>
            </ul>
        </div>""",
        
        """<div class="investor-section">
            <h2>Investor Relations</h2>
            <nav class="ir-nav">
                <a href="/investors/overview">Overview</a>
                <a href="/investors/financials">Financials</a>
                <a href="/investors/news-events">News & Events</a>
            </nav>
        </div>""",
        
        """<section class="announcements">
            <h2>Company Announcements</h2>
            <div class="announcement-grid">
                <article><a href="/announcements/1">Announcement 1</a></article>
                <article><a href="/announcements/2">Announcement 2</a></article>
            </div>
        </section>"""
    ]
    
    logger.info(f"测试分片数量: {len(html_fragments)}")
    
    # 注意：这里只是测试结构，实际的AI分析需要有效的API密钥
    logger.info("注意: 实际AI分析需要有效的OpenAI API密钥")
    logger.info("当前仅测试分批处理的结构和逻辑")
    
    # 测试URL优化功能
    test_urls = [
        "https://example.com/investors",
        "https://example.com/investors/",  # 重复（尾部斜杠）
        "https://example.com/investors?tab=overview",
        "https://example.com/investors?tab=news",
        "https://example.com/press-releases",
        "https://example.com/press-releases",  # 完全重复
        "https://example.com/sec-filings",
        "invalid-url",  # 无效URL
        ""  # 空URL
    ]
    
    logger.info(f"测试URL数量: {len(test_urls)}")
    optimized_urls = ai_analyzer._optimize_urls(test_urls)
    logger.info(f"优化后URL数量: {len(optimized_urls)}")
    logger.info("优化后的URL:")
    for url in optimized_urls:
        logger.info(f"  - {url}")
    
    # 测试XPath规则优化功能
    test_xpath_rules = [
        "//div[@class='news-list']//a[@href]",
        "//div[@class='news-list']//a",  # 更通用的规则
        "//div[contains(@class, 'news')]//a[@href]",
        "//div[contains(@class, 'news')]//a[@href]",  # 完全重复
        "//section[@class='press-releases']//a",
        "//ul[@class='nav']//a[contains(@href, '/news')]",
        "",  # 空规则
        "//div[@class='news-list']//a[@href]"  # 重复
    ]
    
    logger.info(f"测试XPath规则数量: {len(test_xpath_rules)}")
    optimized_xpath = ai_analyzer._optimize_xpath_rules(test_xpath_rules)
    logger.info(f"优化后XPath规则数量: {len(optimized_xpath)}")
    logger.info("优化后的XPath规则:")
    for rule in optimized_xpath:
        logger.info(f"  - {rule}")

def test_complete_workflow():
    """测试完整的工作流程"""
    logger.info("=" * 60)
    logger.info("测试完整工作流程")
    logger.info("=" * 60)
    
    # 注意：这里只是测试结构，实际运行需要有效的API密钥和网络连接
    logger.info("注意: 完整工作流程测试需要:")
    logger.info("1. 有效的Google Search API密钥")
    logger.info("2. 有效的OpenAI API密钥")
    logger.info("3. 网络连接")
    logger.info("当前仅测试代码结构和逻辑")
    
    try:
        research = SearchResearchClass()
        logger.info("SearchResearchClass 初始化成功")
        
        # 这里可以添加更多的结构测试
        logger.info("所有组件初始化正常")
        
    except Exception as e:
        logger.error(f"工作流程测试失败: {e}")

def main():
    """主测试函数"""
    logger.info("开始HTML内容清理和分批处理优化测试")
    logger.info("测试时间: " + time.strftime("%Y-%m-%d %H:%M:%S"))
    
    try:
        # 测试HTML清理
        cleaned_html = test_html_cleaning()
        
        # 测试HTML分割
        fragments = test_html_splitting()
        
        # 测试分批分析
        test_batch_analysis()
        
        # 测试完整工作流程
        test_complete_workflow()
        
        logger.info("=" * 60)
        logger.info("所有测试完成")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
